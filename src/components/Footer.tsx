import Link from 'next/link';
import { Heart, Mail, Shield, Send } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8 mb-12">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xl">P</span>
              </div>
              <div>
                <div className="text-2xl font-bold">PlacerViva</div>
                <div className="text-sm text-pink-400">Tu asesor de placer personal</div>
              </div>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Te ofrecemos recomendaciones de productos seguras y fiables, junto con guías de compra profesionales
              para que tu vida placentera comience aquí.
            </p>

            {/* Trust Badges */}
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="flex items-center space-x-2">
                <div className="text-2xl">🔒</div>
                <div className="text-sm text-gray-300">Privacidad Garantizada</div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="text-2xl">⭐</div>
                <div className="text-sm text-gray-300">Calidad Asegurada</div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="text-2xl">🤝</div>
                <div className="text-sm text-gray-300">Servicio Atento</div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="text-2xl">👍</div>
                <div className="text-sm text-gray-300">Opiniones Positivas</div>
              </div>
            </div>
          </div>

          {/* Products & Services */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="text-xl">🛍️</div>
              <h3 className="text-lg font-semibold">Productos y Servicios</h3>
            </div>
            <ul className="space-y-3">
              <li>
                <Link href="/categoria/vibradores" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>📦</span>
                  <span>Categorías de Productos</span>
                </Link>
              </li>
              <li>
                <Link href="/quiz" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>🎯</span>
                  <span>Quiz Divertido</span>
                </Link>
              </li>
              <li>
                <Link href="/explorar" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>🧭</span>
                  <span>Explorar Artículos</span>
                </Link>
              </li>
              <li>
                <Link href="/nuevos" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>✨</span>
                  <span>Nuevos Productos</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* Customer Support */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="text-xl">🛡️</div>
              <h3 className="text-lg font-semibold">Soporte al Cliente</h3>
            </div>
            <ul className="space-y-3">
              <li>
                <Link href="/explorar?category=uso" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>📋</span>
                  <span>Guías de Uso</span>
                </Link>
              </li>
              <li>
                <Link href="/explorar?category=seguridad" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>🔒</span>
                  <span>Instrucciones de Seguridad</span>
                </Link>
              </li>
              <li>
                <Link href="/explorar?category=cuidado" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>🧼</span>
                  <span>Limpieza y Cuidado</span>
                </Link>
              </li>
              <li>
                <Link href="/faq" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>❓</span>
                  <span>Preguntas Frecuentes</span>
                </Link>
              </li>
            </ul>
          </div>

          {/* About Us */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="text-xl">🏢</div>
              <h3 className="text-lg font-semibold">Sobre Nosotros</h3>
            </div>
            <ul className="space-y-3">
              <li>
                <Link href="/sobre-nosotros" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>📚</span>
                  <span>Nuestra Historia</span>
                </Link>
              </li>
              <li>
                <Link href="/contacto" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>📞</span>
                  <span>Contáctanos</span>
                </Link>
              </li>
              <li>
                <Link href="/socios" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>🤝</span>
                  <span>Socios</span>
                </Link>
              </li>
              <li>
                <Link href="/trabajo" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
                  <span>💼</span>
                  <span>Únete al Equipo</span>
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="bg-gray-800 rounded-xl p-8 mb-12">
          <div className="flex items-center space-x-2 mb-4">
            <div className="text-2xl">📧</div>
            <h3 className="text-xl font-semibold">Suscríbete al Boletín</h3>
          </div>
          <p className="text-gray-300 mb-6">
            Suscríbete a nuestro correo para recibir las últimas recomendaciones de productos y ofertas exclusivas.
          </p>
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="email"
              placeholder="Ingresa tu correo electrónico"
              className="flex-1 px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
            <button className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-all flex items-center space-x-2">
              <Send className="h-4 w-4" />
              <span>Suscribirse Ahora</span>
            </button>
          </div>
        </div>

        {/* Social Media */}
        <div className="text-center mb-8">
          <h4 className="text-lg font-semibold mb-4">Síguenos en nuestras redes sociales</h4>
          <div className="flex justify-center space-x-6">
            <Link href="https://discord.com/invite/JEjdkQuHz4" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
              <div className="text-2xl">💬</div>
              <span className="hidden sm:inline">Únete a la discusión</span>
            </Link>
            <Link href="https://www.linkedin.com/company/heyboss-xyz/" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
              <div className="text-2xl">💼</div>
              <span className="hidden sm:inline">Sigue nuestras noticias</span>
            </Link>
            <Link href="https://x.com/heybossAI" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
              <div className="text-2xl">🐦</div>
              <span className="hidden sm:inline">Obtén las últimas noticias</span>
            </Link>
            <Link href="https://www.youtube.com/@heyboss-xyz" className="flex items-center space-x-2 text-gray-300 hover:text-pink-400 transition-colors">
              <div className="text-2xl">📺</div>
              <span className="hidden sm:inline">Mira nuestros videos</span>
            </Link>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">P</span>
              </div>
              <p className="text-gray-400 text-sm">
                © 2025 PlacerViva. Todos los derechos reservados.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
              <Link href="/privacidad" className="flex items-center space-x-1 text-gray-400 hover:text-pink-400 transition-colors text-sm">
                <span>🔐</span>
                <span>Política de Privacidad</span>
              </Link>
              <Link href="/terminos" className="flex items-center space-x-1 text-gray-400 hover:text-pink-400 transition-colors text-sm">
                <span>📄</span>
                <span>Términos y Condiciones</span>
              </Link>
            </div>
          </div>

          <div className="flex items-center justify-center mt-6 text-gray-400 text-sm">
            <div className="text-lg mr-2">✨</div>
            <span>Vida de Calidad • Seguridad y Confianza • Privacidad Protegida</span>
            <div className="text-lg ml-2">✨</div>
          </div>
        </div>
      </div>
    </footer>
  );
}
