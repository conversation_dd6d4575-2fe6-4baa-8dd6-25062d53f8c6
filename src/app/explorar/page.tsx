'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { Shuffle, Filter, Compass, Star, ArrowRight } from 'lucide-react';

interface RandomArticle {
  id: string;
  title: string;
  excerpt: string;
  slug: string;
  coverImage?: string;
  category: string;
  readTime: string;
  publishedAt?: Date;
  product?: {
    id: string;
    name: string;
    category: string;
    images: string[];
  } | null;
}

const categories = [
  { value: 'all', label: 'Todas las Categorías', emoji: '🌟' },
  { value: 'vibrators', label: 'Vibradores', emoji: '💫' },
  { value: 'bullets', label: 'Bullets', emoji: '✨' },
  { value: 'nipple-clamps', label: 'Pinzas', emoji: '🔗' },
  { value: 'nipple-suckers', label: 'Succionadores', emoji: '💋' },
  { value: 'cock-rings', label: 'Anillos', emoji: '💍' },
  { value: 'masturbators', label: 'Masturbadores', emoji: '🎯' },
];

export default function ExplorarPage() {
  const [articles, setArticles] = useState<RandomArticle[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  const fetchRandomArticles = async (category: string = 'all') => {
    try {
      setRefreshing(true);
      const response = await fetch(`/api/articles/random?category=${category}&limit=12`);
      const data = await response.json();
      
      if (data.success) {
        setArticles(data.data);
      }
    } catch (error) {
      console.error('Error fetching articles:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchRandomArticles(selectedCategory);
  }, [selectedCategory]);

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setLoading(true);
  };

  const handleRefresh = () => {
    fetchRandomArticles(selectedCategory);
  };

  const getArticleImage = (article: RandomArticle) => {
    if (article.coverImage) {
      return article.coverImage;
    }
    if (article.product?.images && article.product.images.length > 0) {
      return article.product.images[0];
    }
    return null;
  };

  const getCategoryEmoji = (category: string) => {
    const cat = categories.find(c => c.value === category);
    return cat?.emoji || '📝';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50">
      <Navbar />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-100 to-purple-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center items-center mb-6">
              <div className="text-6xl mr-4">🧭</div>
              <div className="text-2xl font-medium text-pink-600">Descubre Contenido</div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Explorar
              </span>{' '}
              Artículos
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Descubre contenido fascinante de forma aleatoria. Cada visita es una nueva aventura 
              llena de recomendaciones personalizadas y consejos expertos.
            </p>
            
            {/* Category Filter */}
            <div className="flex flex-wrap justify-center gap-3 mb-8">
              {categories.map((category) => (
                <button
                  key={category.value}
                  onClick={() => handleCategoryChange(category.value)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-full font-medium transition-all transform hover:scale-105 ${
                    selectedCategory === category.value
                      ? 'bg-gradient-to-r from-pink-600 to-purple-600 text-white shadow-lg'
                      : 'bg-white text-gray-700 hover:bg-pink-50 shadow-md'
                  }`}
                >
                  <span>{category.emoji}</span>
                  <span>{category.label}</span>
                </button>
              ))}
            </div>

            {/* Refresh Button */}
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all transform hover:scale-105 shadow-lg disabled:opacity-50"
            >
              <Shuffle className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
              <span>{refreshing ? 'Refrescando...' : 'Refrescar Contenido'}</span>
            </button>
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                  <div className="h-48 bg-gray-200"></div>
                  <div className="p-6">
                    <div className="h-4 bg-gray-200 rounded mb-3"></div>
                    <div className="h-6 bg-gray-200 rounded mb-3"></div>
                    <div className="h-4 bg-gray-200 rounded mb-4"></div>
                    <div className="h-10 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : articles.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {articles.map((article) => {
                const articleImage = getArticleImage(article);
                return (
                  <div
                    key={article.id}
                    className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all transform hover:scale-105 group"
                  >
                    <div className="h-48 bg-gradient-to-br from-pink-100 to-purple-100 flex items-center justify-center relative overflow-hidden">
                      {articleImage ? (
                        <Image
                          src={articleImage}
                          alt={article.title}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                      ) : (
                        <div className="text-8xl">{getCategoryEmoji(article.category)}</div>
                      )}
                    </div>
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        <span className="bg-pink-100 text-pink-600 px-3 py-1 rounded-full text-sm font-medium">
                          {categories.find(c => c.value === article.category)?.label || article.category}
                        </span>
                        <span className="text-gray-500 text-sm flex items-center">
                          <span className="mr-1">📖</span>
                          {article.readTime}
                        </span>
                      </div>
                      
                      <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2 group-hover:text-pink-600 transition-colors">
                        {article.title}
                      </h3>
                      
                      <p className="text-gray-600 mb-4 line-clamp-3">
                        {article.excerpt}
                      </p>
                      
                      <Link
                        href={`/articulo/${article.slug}`}
                        className="inline-flex items-center space-x-2 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all w-full justify-center group"
                      >
                        <span>Leer Artículo</span>
                        <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                      </Link>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                No se encontraron artículos
              </h3>
              <p className="text-gray-600 mb-8">
                No hay artículos disponibles en esta categoría. Prueba con otra categoría.
              </p>
              <button
                onClick={() => handleCategoryChange('all')}
                className="bg-pink-600 hover:bg-pink-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Ver Todas las Categorías
              </button>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-50 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex justify-center items-center mb-6">
            <Star className="h-8 w-8 text-yellow-500 mr-2" />
            <Compass className="h-8 w-8 text-pink-600 mr-2" />
            <Star className="h-8 w-8 text-yellow-500" />
          </div>
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            ¿Necesitas Recomendaciones Personalizadas?
          </h3>
          <p className="text-lg text-gray-600 mb-8">
            Prueba nuestro quiz interactivo para obtener recomendaciones específicas basadas en tus preferencias y necesidades.
          </p>
          <Link
            href="/quiz"
            className="inline-flex items-center space-x-3 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all transform hover:scale-105 shadow-lg"
          >
            <span>🎯</span>
            <span>Hacer Quiz Personalizado</span>
            <span>✨</span>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
}
